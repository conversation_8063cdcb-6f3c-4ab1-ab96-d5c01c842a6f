# GitHub Inactive Users Finder

A Python application to identify inactive users in a GitHub organization. This tool helps organization administrators find members who haven't had any activity in the last 3 months.

## Features

- 🔍 **Comprehensive Activity Detection**: Checks both user events and commit activity
- 📊 **Detailed Reporting**: Generates formatted reports with user information
- 👤 **Rich User Details**: Includes full names, emails, company, and location information
- 📄 **Multiple Export Formats**: Text reports and CSV exports for easy data handling
- 🚀 **Easy to Use**: Simple command-line interface
- ⚡ **Efficient**: Uses pagination to handle large organizations
- 🔒 **Secure**: Uses GitHub personal access tokens for authentication

## What Counts as Activity?

The tool considers a user "active" if they have performed any of the following actions within the specified time period (default: 90 days):

- Created issues or pull requests
- Commented on issues or pull requests
- Pushed commits to any repository
- Starred or watched repositories
- Created or deleted repositories
- Any other GitHub events tracked by the Events API

## Prerequisites

- Python 3.7 or higher
- A GitHub personal access token with appropriate permissions

## Installation

1. **Clone or download this repository**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up your GitHub token**:
   
   Create a GitHub personal access token:
   - Go to [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
   - Click "Generate new token (classic)"
   - Select the following scopes:
     - `read:org` - to read organization membership
     - `read:user` - to read user information
   - Copy the generated token

4. **Configure environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env and add your GitHub token
   ```

## Usage

### Basic Usage

```bash
python github_inactive_users.py --org YOUR_ORG_NAME
```

### With Custom Inactive Period

```bash
# Find users inactive for 60 days or more
python github_inactive_users.py --org YOUR_ORG_NAME --days 60

# Find users inactive for 6 months (180 days) or more
python github_inactive_users.py --org YOUR_ORG_NAME --days 180
```

### With Token as Argument

```bash
python github_inactive_users.py --org YOUR_ORG_NAME --token YOUR_GITHUB_TOKEN --days 30
```

### Save Report to File

```bash
python github_inactive_users.py --org YOUR_ORG_NAME --days 90 --output inactive_users_report.txt
```

### Command Line Options

- `--org` (required): GitHub organization name
- `--days` (optional): Number of days to consider a user inactive (default: 90)
- `--token` (optional): GitHub personal access token (can also be set via GITHUB_TOKEN environment variable)
- `--output` (optional): Output file path to save the report

## Example Output

```
Fetching members of organization: myorg
Found 25 members in the organization

Checking activity for 25 members...
Inactive period: 90 days
Cutoff date: 2024-03-19
--------------------------------------------------
[1/25] Checking alice... ✅ Active (last activity: 2024-06-15T10:30:00Z)
[2/25] Checking bob... ❌ INACTIVE
[3/25] Checking charlie... ✅ Active (last activity: 2024-06-10T14:22:00Z)
...

============================================================

GitHub Inactive Users Report
Organization: myorg
Generated: 2024-06-19 15:30:45
Inactive Period: 90 days
Cutoff Date: 2024-03-19

Total Inactive Users: 3

Inactive Users:
==================================================
• bob (User)
  Profile: https://github.com/bob
  Last Activity: Unknown

• inactive-user (User)
  Profile: https://github.com/inactive-user
  Last Activity: Unknown

• old-member (User)
  Profile: https://github.com/old-member
  Last Activity: Unknown
```

## Rate Limiting

The GitHub API has rate limits:
- **Authenticated requests**: 5,000 requests per hour
- **Search API**: 30 requests per minute

The script automatically handles these rate limits:
- **Search API calls are throttled** to stay within the 30 requests/minute limit
- **Automatic waiting** when rate limits are approached
- **Progress indicators** show when the script is waiting for rate limits

For large organizations, the script may take some time to complete due to these limits, especially the Search API rate limit. The script will show progress and wait times as needed.

## Troubleshooting

### Common Issues

1. **"Error: GitHub token is required"**
   - Make sure you've set the `GITHUB_TOKEN` environment variable or use the `--token` argument

2. **"403 Forbidden" errors**
   - Check that your token has the required scopes (`read:org`, `read:user`)
   - Ensure you have permission to view the organization's members

3. **"404 Not Found" for organization**
   - Verify the organization name is correct
   - Ensure the organization exists and you have access to it

4. **Rate limit errors**
   - The script will automatically handle rate limits, but for very large organizations, consider running during off-peak hours

### Token Permissions

Your GitHub token needs these permissions:
- `read:org` - Required to list organization members
- `read:user` - Required to access user activity information

## Security Notes

- Never commit your GitHub token to version control
- Use environment variables or the `.env` file to store sensitive information
- Consider using a token with minimal required permissions
- Regularly rotate your access tokens

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.

## License

This project is open source and available under the MIT License.
