#!/bin/bash

# GitHub Inactive Users Finder - Setup Script

echo "🚀 Setting up GitHub Inactive Users Finder..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.7 or higher."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip."
    exit 1
fi

echo "✅ pip3 found"

# Install dependencies
echo "📦 Installing dependencies..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it and add your GitHub token."
else
    echo "ℹ️  .env file already exists"
fi

# Make scripts executable
chmod +x github_inactive_users.py
chmod +x example_usage.py

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit the .env file and add your GitHub token"
echo "2. Run the script: python3 github_inactive_users.py --org YOUR_ORG_NAME"
echo ""
echo "For more information, see README.md"
