AWSTemplateFormatVersion: '2010-09-09'
Description: 'GitHub Inactive Users Finder Lambda Function with Slack Integration'

Parameters:
  GitHubToken:
    Type: String
    Description: GitHub Personal Access Token
    NoEcho: true
    
  GitHubOrg:
    Type: String
    Description: GitHub Organization Name
    
  SlackWebhookUrl:
    Type: String
    Description: Slack Webhook URL
    NoEcho: true
    
  InactiveDays:
    Type: Number
    Description: Number of days to consider a user inactive
    Default: 90
    
  MaxUsersToCheck:
    Type: Number
    Description: Maximum number of users to check (to avoid Lambda timeout)
    Default: 50
    
  ScheduleExpression:
    Type: String
    Description: CloudWatch Events schedule expression (e.g., 'rate(7 days)' for weekly)
    Default: 'rate(7 days)'

Resources:
  # IAM Role for Lambda
  GitHubInactiveUsersLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub '${AWS::StackName}-lambda-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:*'

  # Lambda Function
  GitHubInactiveUsersLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub '${AWS::StackName}-github-inactive-users'
      Runtime: python3.9
      Handler: lambda_function.lambda_handler
      Role: !GetAtt GitHubInactiveUsersLambdaRole.Arn
      Timeout: 900  # 15 minutes
      MemorySize: 512
      Environment:
        Variables:
          GITHUB_TOKEN: !Ref GitHubToken
          GITHUB_ORG: !Ref GitHubOrg
          SLACK_WEBHOOK_URL: !Ref SlackWebhookUrl
          INACTIVE_DAYS: !Ref InactiveDays
          MAX_USERS_TO_CHECK: !Ref MaxUsersToCheck
      Code:
        ZipFile: |
          # Placeholder code - replace with actual deployment package
          def lambda_handler(event, context):
              return {'statusCode': 200, 'body': 'Please deploy the actual code'}

  # CloudWatch Events Rule for Scheduling
  GitHubInactiveUsersScheduleRule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub '${AWS::StackName}-schedule'
      Description: 'Schedule for GitHub Inactive Users Check'
      ScheduleExpression: !Ref ScheduleExpression
      State: ENABLED
      Targets:
        - Arn: !GetAtt GitHubInactiveUsersLambda.Arn
          Id: GitHubInactiveUsersTarget

  # Permission for CloudWatch Events to invoke Lambda
  LambdaInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref GitHubInactiveUsersLambda
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt GitHubInactiveUsersScheduleRule.Arn

  # CloudWatch Log Group
  GitHubInactiveUsersLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/${GitHubInactiveUsersLambda}'
      RetentionInDays: 14

Outputs:
  LambdaFunctionName:
    Description: 'Name of the Lambda function'
    Value: !Ref GitHubInactiveUsersLambda
    Export:
      Name: !Sub '${AWS::StackName}-LambdaFunctionName'
      
  LambdaFunctionArn:
    Description: 'ARN of the Lambda function'
    Value: !GetAtt GitHubInactiveUsersLambda.Arn
    Export:
      Name: !Sub '${AWS::StackName}-LambdaFunctionArn'
      
  ScheduleRuleName:
    Description: 'Name of the CloudWatch Events rule'
    Value: !Ref GitHubInactiveUsersScheduleRule
    Export:
      Name: !Sub '${AWS::StackName}-ScheduleRuleName'
