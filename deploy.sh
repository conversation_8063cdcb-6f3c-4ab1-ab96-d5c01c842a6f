#!/bin/bash

# GitHub Inactive Users Lambda Deployment Script

set -e

# Configuration
STACK_NAME="github-inactive-users"
REGION="us-east-1"  # Change this to your preferred region
LAMBDA_ZIP="github-inactive-users-lambda.zip"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 GitHub Inactive Users Lambda Deployment${NC}"
echo "=============================================="

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ AWS CLI configured${NC}"

# Get parameters from user
echo ""
echo -e "${YELLOW}📝 Please provide the following information:${NC}"

read -p "GitHub Organization Name: " GITHUB_ORG
if [ -z "$GITHUB_ORG" ]; then
    echo -e "${RED}❌ GitHub organization name is required${NC}"
    exit 1
fi

read -s -p "GitHub Personal Access Token: " GITHUB_TOKEN
echo ""
if [ -z "$GITHUB_TOKEN" ]; then
    echo -e "${RED}❌ GitHub token is required${NC}"
    exit 1
fi

read -s -p "Slack Webhook URL: " SLACK_WEBHOOK_URL
echo ""
if [ -z "$SLACK_WEBHOOK_URL" ]; then
    echo -e "${RED}❌ Slack webhook URL is required${NC}"
    exit 1
fi

read -p "Inactive days threshold (default: 90): " INACTIVE_DAYS
INACTIVE_DAYS=${INACTIVE_DAYS:-90}

read -p "Max users to check per run (default: 50): " MAX_USERS
MAX_USERS=${MAX_USERS:-50}

read -p "Schedule expression (default: rate(7 days)): " SCHEDULE
SCHEDULE=${SCHEDULE:-"rate(7 days)"}

read -p "AWS Region (default: us-east-1): " AWS_REGION
AWS_REGION=${AWS_REGION:-us-east-1}

echo ""
echo -e "${BLUE}📦 Creating deployment package...${NC}"

# Create a temporary directory for the deployment package
TEMP_DIR=$(mktemp -d)
echo "Using temporary directory: $TEMP_DIR"

# Copy Lambda function code
cp lambda_function.py "$TEMP_DIR/"

# Install dependencies
echo "Installing Python dependencies..."
pip install -r lambda_requirements.txt -t "$TEMP_DIR/" --quiet

# Create deployment package
echo "Creating ZIP file..."
cd "$TEMP_DIR"
zip -r "../$LAMBDA_ZIP" . --quiet
cd - > /dev/null

# Move ZIP to current directory
mv "$TEMP_DIR/../$LAMBDA_ZIP" .

# Clean up temporary directory
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Deployment package created: $LAMBDA_ZIP${NC}"

# Deploy CloudFormation stack
echo ""
echo -e "${BLUE}☁️ Deploying CloudFormation stack...${NC}"

aws cloudformation deploy \
    --template-file cloudformation-template.yaml \
    --stack-name "$STACK_NAME" \
    --region "$AWS_REGION" \
    --capabilities CAPABILITY_NAMED_IAM \
    --parameter-overrides \
        GitHubToken="$GITHUB_TOKEN" \
        GitHubOrg="$GITHUB_ORG" \
        SlackWebhookUrl="$SLACK_WEBHOOK_URL" \
        InactiveDays="$INACTIVE_DAYS" \
        MaxUsersToCheck="$MAX_USERS" \
        ScheduleExpression="$SCHEDULE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ CloudFormation stack deployed successfully${NC}"
else
    echo -e "${RED}❌ CloudFormation deployment failed${NC}"
    exit 1
fi

# Update Lambda function code
echo ""
echo -e "${BLUE}📤 Updating Lambda function code...${NC}"

FUNCTION_NAME=$(aws cloudformation describe-stacks \
    --stack-name "$STACK_NAME" \
    --region "$AWS_REGION" \
    --query 'Stacks[0].Outputs[?OutputKey==`LambdaFunctionName`].OutputValue' \
    --output text)

aws lambda update-function-code \
    --function-name "$FUNCTION_NAME" \
    --zip-file "fileb://$LAMBDA_ZIP" \
    --region "$AWS_REGION" > /dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Lambda function code updated successfully${NC}"
else
    echo -e "${RED}❌ Lambda function code update failed${NC}"
    exit 1
fi

# Clean up deployment package
rm "$LAMBDA_ZIP"

echo ""
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Deployment Summary:${NC}"
echo "Stack Name: $STACK_NAME"
echo "Region: $AWS_REGION"
echo "Function Name: $FUNCTION_NAME"
echo "GitHub Org: $GITHUB_ORG"
echo "Inactive Days: $INACTIVE_DAYS"
echo "Max Users: $MAX_USERS"
echo "Schedule: $SCHEDULE"
echo ""
echo -e "${BLUE}🔧 Next Steps:${NC}"
echo "1. The Lambda function is now deployed and scheduled"
echo "2. Check CloudWatch Logs for execution details"
echo "3. Test the function manually if needed:"
echo "   aws lambda invoke --function-name $FUNCTION_NAME --region $AWS_REGION response.json"
echo "4. Monitor Slack for reports"
echo ""
echo -e "${YELLOW}⚠️ Important Notes:${NC}"
echo "• The function will run automatically based on the schedule: $SCHEDULE"
echo "• Check CloudWatch Logs if you don't receive Slack notifications"
echo "• Adjust MAX_USERS_TO_CHECK if you hit Lambda timeout limits"
echo "• GitHub API rate limits are handled automatically"
