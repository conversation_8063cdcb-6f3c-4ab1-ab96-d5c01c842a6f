#!/usr/bin/env python3
"""
Example usage of the GitHub Inactive Users Finder

This script demonstrates how to use the GitHubInactiveUsersFinder class programmatically.
"""

import os
from github_inactive_users import GitHubInactiveUsersFinder

def main():
    # Example usage - replace with your actual values
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN', 'your_token_here')
    ORG_NAME = 'your-org-name'  # Replace with your organization name
    
    if GITHUB_TOKEN == 'your_token_here':
        print("Please set your GITHUB_TOKEN environment variable or update this script")
        return
    
    try:
        # Initialize the finder
        print(f"Initializing GitHub Inactive Users Finder for organization: {ORG_NAME}")
        finder = GitHubInactiveUsersFinder(GITHUB_TOKEN, ORG_NAME)
        
        # Get organization members
        members = finder.get_organization_members()
        print(f"Found {len(members)} members in the organization")
        
        # Find inactive users
        print("\nFinding inactive users...")
        inactive_users = finder.find_inactive_users()
        
        # Generate and display report
        report = finder.generate_report(inactive_users)
        print(report)
        
        # Optionally save to file
        with open(f'{ORG_NAME}_inactive_users_report.txt', 'w') as f:
            f.write(report)
        print(f"Report saved to {ORG_NAME}_inactive_users_report.txt")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
