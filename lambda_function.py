"""
AWS Lambda function for GitHub Inactive Users Finder with Slack integration.

This Lambda function checks for inactive users in a GitHub organization
and sends a report to a Slack channel.
"""

import json
import os
import boto3
import requests
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional

class GitHubInactiveUsersFinder:
    def __init__(self, token: str, org: str, inactive_days: int = 90):
        """
        Initialize the GitHub API client for Lambda.
        
        Args:
            token: GitHub personal access token
            org: GitHub organization name
            inactive_days: Number of days to consider a user inactive
        """
        self.token = token
        self.org = org
        self.inactive_days = inactive_days
        self.base_url = "https://api.github.com"
        self.headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "GitHub-Inactive-Users-Lambda"
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Calculate the cutoff date
        self.cutoff_date = datetime.now() - timedelta(days=inactive_days)
        
        # Rate limiting - more conservative for Lambda
        self.batch_size = 10  # Smaller batches for Lambda
        self.batch_wait_time = 30  # Shorter wait time
        
    def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make a request to the GitHub API with error handling."""
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            return None
    
    def _get_paginated_results(self, url: str, params: Optional[Dict] = None) -> List[Dict]:
        """Get all results from a paginated GitHub API endpoint."""
        all_results = []
        page = 1
        per_page = 100
        
        if params is None:
            params = {}
        
        params.update({"per_page": per_page, "page": page})
        
        while True:
            response = self._make_request(url, params)
            if not response:
                break
                
            if isinstance(response, list):
                results = response
            else:
                results = response.get('items', [])
            
            if not results:
                break
                
            all_results.extend(results)
            
            # Check if there are more pages
            if len(results) < per_page:
                break
                
            page += 1
            params["page"] = page
            
        return all_results
    
    def get_organization_members(self) -> List[Dict]:
        """Get all members of the organization."""
        print(f"Fetching members of organization: {self.org}")
        url = f"{self.base_url}/orgs/{self.org}/members"
        members = self._get_paginated_results(url)
        print(f"Found {len(members)} members in the organization")
        return members
    
    def get_user_details(self, username: str) -> Dict:
        """Get detailed user information including name and email."""
        url = f"{self.base_url}/users/{username}"
        user_data = self._make_request(url)
        
        if user_data:
            return {
                "name": user_data.get("name") or "N/A",
                "email": user_data.get("email") or "N/A",
                "company": user_data.get("company") or "N/A",
                "location": user_data.get("location") or "N/A"
            }
        else:
            return {
                "name": "N/A",
                "email": "N/A", 
                "company": "N/A",
                "location": "N/A"
            }
    
    def get_user_activity(self, username: str) -> Dict:
        """Check user activity in the specified time period."""
        activity_info = {
            "username": username,
            "has_recent_activity": False,
            "last_activity_date": None,
            "activity_types": []
        }
        
        # Check recent events
        events_url = f"{self.base_url}/users/{username}/events"
        events = self._get_paginated_results(events_url)
        
        recent_events = []
        for event in events:
            event_date = datetime.strptime(event['created_at'], "%Y-%m-%dT%H:%M:%SZ")
            if event_date >= self.cutoff_date:
                recent_events.append(event)
                activity_info["activity_types"].append(event['type'])
        
        if recent_events:
            activity_info["has_recent_activity"] = True
            latest_event = max(recent_events, key=lambda x: x['created_at'])
            activity_info["last_activity_date"] = latest_event['created_at']
        
        # Also check recent commits (simplified for Lambda)
        search_url = f"{self.base_url}/search/commits"
        since_date = self.cutoff_date.strftime("%Y-%m-%d")
        search_params = {
            "q": f"author:{username} author-date:>{since_date}",
            "sort": "author-date",
            "order": "desc"
        }
        
        commits_response = self._make_request(search_url, search_params)
        if commits_response and commits_response.get('total_count', 0) > 0:
            activity_info["has_recent_activity"] = True
            if not activity_info["last_activity_date"]:
                latest_commit = commits_response['items'][0]
                activity_info["last_activity_date"] = latest_commit['commit']['author']['date']
            activity_info["activity_types"].append("CommitEvent")
        
        return activity_info
    
    def find_inactive_users(self, max_users: int = 50) -> List[Dict]:
        """
        Find inactive users in the organization (limited for Lambda).
        
        Args:
            max_users: Maximum number of users to check (to avoid Lambda timeout)
            
        Returns:
            List of inactive users with their information
        """
        members = self.get_organization_members()
        inactive_users = []
        
        # Limit the number of users to check to avoid Lambda timeout
        members_to_check = members[:max_users]
        
        print(f"Checking activity for {len(members_to_check)} members (limited from {len(members)} total)...")
        print(f"Inactive period: {self.inactive_days} days")
        print(f"Cutoff date: {self.cutoff_date.strftime('%Y-%m-%d')}")
        
        # Process users in batches with rate limiting
        for batch_start in range(0, len(members_to_check), self.batch_size):
            batch_end = min(batch_start + self.batch_size, len(members_to_check))
            batch_members = members_to_check[batch_start:batch_end]
            
            print(f"Processing batch {batch_start//self.batch_size + 1} (users {batch_start + 1}-{batch_end})")
            
            for i, member in enumerate(batch_members):
                global_index = batch_start + i + 1
                username = member['login']
                print(f"[{global_index}/{len(members_to_check)}] Checking {username}...")
                
                activity = self.get_user_activity(username)
                
                if not activity["has_recent_activity"]:
                    user_details = self.get_user_details(username)
                    
                    inactive_users.append({
                        "username": username,
                        "name": user_details["name"],
                        "email": user_details["email"],
                        "company": user_details["company"],
                        "location": user_details["location"],
                        "profile_url": member['html_url'],
                        "user_type": member.get('type', 'User'),
                        "last_activity": activity["last_activity_date"]
                    })
                    print(f"  ❌ INACTIVE")
                else:
                    print(f"  ✅ Active")
            
            # Wait between batches (except for the last batch)
            if batch_end < len(members_to_check):
                print(f"Waiting {self.batch_wait_time}s to avoid rate limits...")
                time.sleep(self.batch_wait_time)
        
        return inactive_users


class SlackNotifier:
    def __init__(self, webhook_url: str):
        """Initialize Slack notifier with webhook URL."""
        self.webhook_url = webhook_url
    
    def format_slack_message(self, org: str, inactive_users: List[Dict], inactive_days: int, total_checked: int, total_members: int) -> Dict:
        """Format the inactive users report for Slack."""
        
        # Create header
        if inactive_users:
            color = "warning" if len(inactive_users) <= 5 else "danger"
            title = f"⚠️ GitHub Inactive Users Report - {org}"
        else:
            color = "good"
            title = f"✅ GitHub Activity Report - {org}"
        
        # Create summary
        summary = f"*Organization:* {org}\n"
        summary += f"*Inactive Period:* {inactive_days} days\n"
        summary += f"*Users Checked:* {total_checked} of {total_members} total members\n"
        summary += f"*Inactive Users Found:* {len(inactive_users)}\n"
        summary += f"*Generated:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}"
        
        # Create user list
        if inactive_users:
            user_list = ""
            for user in inactive_users[:10]:  # Limit to first 10 users to avoid message size limits
                user_list += f"• *{user['username']}*"
                if user['name'] != 'N/A':
                    user_list += f" ({user['name']})"
                if user['email'] != 'N/A':
                    user_list += f" - {user['email']}"
                user_list += f"\n  Profile: {user['profile_url']}\n"
            
            if len(inactive_users) > 10:
                user_list += f"\n... and {len(inactive_users) - 10} more users"
        else:
            user_list = "🎉 No inactive users found! All checked members have been active."
        
        # Create Slack message
        message = {
            "attachments": [
                {
                    "color": color,
                    "title": title,
                    "text": summary,
                    "fields": [
                        {
                            "title": "Inactive Users" if inactive_users else "Status",
                            "value": user_list,
                            "short": False
                        }
                    ],
                    "footer": "GitHub Inactive Users Monitor",
                    "ts": int(datetime.now().timestamp())
                }
            ]
        }
        
        return message
    
    def send_to_slack(self, message: Dict) -> bool:
        """Send message to Slack."""
        try:
            response = requests.post(self.webhook_url, json=message)
            response.raise_for_status()
            print("Successfully sent message to Slack")
            return True
        except requests.exceptions.RequestException as e:
            print(f"Error sending message to Slack: {e}")
            return False


def lambda_handler(event, context):
    """
    AWS Lambda handler function.
    
    Expected environment variables:
    - GITHUB_TOKEN: GitHub personal access token
    - GITHUB_ORG: GitHub organization name
    - SLACK_WEBHOOK_URL: Slack webhook URL
    - INACTIVE_DAYS: Number of days to consider inactive (optional, default: 90)
    - MAX_USERS_TO_CHECK: Maximum users to check (optional, default: 50)
    """
    
    try:
        # Get configuration from environment variables
        github_token = os.environ.get('GITHUB_TOKEN')
        github_org = os.environ.get('GITHUB_ORG')
        slack_webhook_url = os.environ.get('SLACK_WEBHOOK_URL')
        inactive_days = int(os.environ.get('INACTIVE_DAYS', '90'))
        max_users = int(os.environ.get('MAX_USERS_TO_CHECK', '50'))
        
        # Validate required environment variables
        if not github_token:
            raise ValueError("GITHUB_TOKEN environment variable is required")
        if not github_org:
            raise ValueError("GITHUB_ORG environment variable is required")
        if not slack_webhook_url:
            raise ValueError("SLACK_WEBHOOK_URL environment variable is required")
        
        print(f"Starting GitHub inactive users check for organization: {github_org}")
        print(f"Configuration: inactive_days={inactive_days}, max_users={max_users}")
        
        # Initialize GitHub finder
        finder = GitHubInactiveUsersFinder(github_token, github_org, inactive_days)
        
        # Get total member count first
        all_members = finder.get_organization_members()
        total_members = len(all_members)
        
        # Find inactive users (limited)
        inactive_users = finder.find_inactive_users(max_users)
        
        print(f"Found {len(inactive_users)} inactive users out of {min(max_users, total_members)} checked")
        
        # Send to Slack
        slack_notifier = SlackNotifier(slack_webhook_url)
        message = slack_notifier.format_slack_message(
            github_org, 
            inactive_users, 
            inactive_days, 
            min(max_users, total_members),
            total_members
        )
        
        success = slack_notifier.send_to_slack(message)
        
        # Return response
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Successfully completed GitHub inactive users check',
                'organization': github_org,
                'inactive_users_found': len(inactive_users),
                'users_checked': min(max_users, total_members),
                'total_members': total_members,
                'slack_sent': success
            })
        }
        
    except Exception as e:
        print(f"Error in lambda_handler: {str(e)}")
        
        # Try to send error to Slack if possible
        try:
            if 'slack_webhook_url' in locals():
                error_message = {
                    "attachments": [
                        {
                            "color": "danger",
                            "title": "❌ GitHub Inactive Users Check Failed",
                            "text": f"Error: {str(e)}",
                            "footer": "GitHub Inactive Users Monitor",
                            "ts": int(datetime.now().timestamp())
                        }
                    ]
                }
                requests.post(slack_webhook_url, json=error_message)
        except:
            pass  # Don't fail if we can't send error to Slack
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e)
            })
        }
