#!/usr/bin/env python3
"""
GitHub Inactive Users Finder

This script identifies inactive users in a GitHub organization.
Inactive users are defined as users who haven't had any activity in the last 3 months.
"""

import os
import sys
import requests
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import argparse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class GitHubInactiveUsersFinder:
    def __init__(self, token: str, org: str, inactive_days: int = 90):
        """
        Initialize the GitHub API client.

        Args:
            token: GitHub personal access token
            org: GitHub organization name
            inactive_days: Number of days to consider a user inactive (default: 90 days / 3 months)
        """
        self.token = token
        self.org = org
        self.inactive_days = inactive_days
        self.base_url = "https://api.github.com"
        self.headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "GitHub-Inactive-Users-Finder"
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # Calculate the cutoff date based on inactive_days parameter
        self.cutoff_date = datetime.now() - timedelta(days=inactive_days)

        # Rate limiting for search API (30 requests per minute)
        self.search_api_calls = []
        self.search_rate_limit = 30  # requests per minute
        self.search_rate_window = 60  # seconds
        
    def _wait_for_search_rate_limit(self):
        """
        Implement rate limiting for GitHub Search API (30 requests per minute).
        """
        current_time = time.time()

        # Remove calls older than the rate window
        self.search_api_calls = [call_time for call_time in self.search_api_calls
                                if current_time - call_time < self.search_rate_window]

        # If we're at the rate limit, wait
        if len(self.search_api_calls) >= self.search_rate_limit:
            oldest_call = min(self.search_api_calls)
            wait_time = self.search_rate_window - (current_time - oldest_call) + 1
            if wait_time > 0:
                print(f"  ⏳ Rate limit reached, waiting {wait_time:.1f} seconds...")
                time.sleep(wait_time)

        # Record this call
        self.search_api_calls.append(current_time)

    def _make_request(self, url: str, params: Optional[Dict] = None, is_search_api: bool = False) -> Optional[Dict]:
        """
        Make a request to the GitHub API with error handling.

        Args:
            url: API endpoint URL
            params: Query parameters
            is_search_api: Whether this is a search API call (for rate limiting)

        Returns:
            JSON response or None if error
        """
        # Apply rate limiting for search API
        if is_search_api:
            self._wait_for_search_rate_limit()

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            return None
    
    def _get_paginated_results(self, url: str, params: Optional[Dict] = None, is_search_api: bool = False) -> List[Dict]:
        """
        Get all results from a paginated GitHub API endpoint.

        Args:
            url: API endpoint URL
            params: Query parameters
            is_search_api: Whether this is a search API call (for rate limiting)

        Returns:
            List of all results across all pages
        """
        all_results = []
        page = 1
        per_page = 100

        if params is None:
            params = {}

        params.update({"per_page": per_page, "page": page})

        while True:
            response = self._make_request(url, params, is_search_api=is_search_api)
            if not response:
                break

            if isinstance(response, list):
                results = response
            else:
                results = response.get('items', [])

            if not results:
                break

            all_results.extend(results)

            # Check if there are more pages
            if len(results) < per_page:
                break

            page += 1
            params["page"] = page

        return all_results
    
    def get_organization_members(self) -> List[Dict]:
        """
        Get all members of the organization.

        Returns:
            List of organization members
        """
        print(f"Fetching members of organization: {self.org}")
        url = f"{self.base_url}/orgs/{self.org}/members"
        members = self._get_paginated_results(url)
        print(f"Found {len(members)} members in the organization")
        return members

    def get_user_details(self, username: str) -> Dict:
        """
        Get detailed user information including name and email.

        Args:
            username: GitHub username

        Returns:
            Dictionary with user details
        """
        url = f"{self.base_url}/users/{username}"
        user_data = self._make_request(url)

        if user_data:
            return {
                "name": user_data.get("name") or "N/A",
                "email": user_data.get("email") or "N/A",
                "company": user_data.get("company") or "N/A",
                "location": user_data.get("location") or "N/A",
                "bio": user_data.get("bio") or "N/A"
            }
        else:
            return {
                "name": "N/A",
                "email": "N/A",
                "company": "N/A",
                "location": "N/A",
                "bio": "N/A"
            }
    
    def get_user_activity(self, username: str) -> Dict:
        """
        Check user activity in the specified time period.

        Args:
            username: GitHub username

        Returns:
            Dictionary with activity information
        """
        activity_info = {
            "username": username,
            "has_recent_activity": False,
            "last_activity_date": None,
            "activity_types": []
        }
        
        # Check recent events
        events_url = f"{self.base_url}/users/{username}/events"
        events = self._get_paginated_results(events_url)
        
        recent_events = []
        for event in events:
            event_date = datetime.strptime(event['created_at'], "%Y-%m-%dT%H:%M:%SZ")
            if event_date >= self.cutoff_date:
                recent_events.append(event)
                activity_info["activity_types"].append(event['type'])
        
        if recent_events:
            activity_info["has_recent_activity"] = True
            # Get the most recent activity date
            latest_event = max(recent_events, key=lambda x: x['created_at'])
            activity_info["last_activity_date"] = latest_event['created_at']
        
        # Also check recent commits (public repositories)
        # This is an additional check as events might not capture all activity
        # Note: This uses the Search API which has stricter rate limits (30 requests/minute)
        search_url = f"{self.base_url}/search/commits"
        since_date = self.cutoff_date.strftime("%Y-%m-%d")
        search_params = {
            "q": f"author:{username} author-date:>{since_date}",
            "sort": "author-date",
            "order": "desc"
        }

        commits_response = self._make_request(search_url, search_params, is_search_api=True)
        if commits_response and commits_response.get('total_count', 0) > 0:
            activity_info["has_recent_activity"] = True
            if not activity_info["last_activity_date"]:
                # Use the most recent commit date if no events were found
                latest_commit = commits_response['items'][0]
                activity_info["last_activity_date"] = latest_commit['commit']['author']['date']
            activity_info["activity_types"].append("CommitEvent")
        
        return activity_info
    
    def find_inactive_users(self) -> List[Dict]:
        """
        Find all inactive users in the organization.
        
        Returns:
            List of inactive users with their information
        """
        members = self.get_organization_members()
        inactive_users = []
        
        print(f"\nChecking activity for {len(members)} members...")
        print(f"Inactive period: {self.inactive_days} days")
        print(f"Cutoff date: {self.cutoff_date.strftime('%Y-%m-%d')}")
        print("-" * 50)
        
        for i, member in enumerate(members, 1):
            username = member['login']
            print(f"[{i}/{len(members)}] Checking {username}...", end=" ")
            
            activity = self.get_user_activity(username)
            
            if not activity["has_recent_activity"]:
                inactive_users.append({
                    "username": username,
                    "profile_url": member['html_url'],
                    "avatar_url": member['avatar_url'],
                    "user_type": member.get('type', 'User'),
                    "last_activity": activity["last_activity_date"]
                })
                print("❌ INACTIVE")
            else:
                print(f"✅ Active (last activity: {activity['last_activity_date']})")
        
        return inactive_users
    
    def generate_report(self, inactive_users: List[Dict]) -> str:
        """
        Generate a formatted report of inactive users.
        
        Args:
            inactive_users: List of inactive users
            
        Returns:
            Formatted report string
        """
        report = f"""
GitHub Inactive Users Report
Organization: {self.org}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Inactive Period: {self.inactive_days} days
Cutoff Date: {self.cutoff_date.strftime('%Y-%m-%d')}

Total Inactive Users: {len(inactive_users)}

"""
        
        if inactive_users:
            report += "Inactive Users:\n"
            report += "=" * 50 + "\n"
            for user in inactive_users:
                report += f"• {user['username']} ({user['user_type']})\n"
                report += f"  Profile: {user['profile_url']}\n"
                report += f"  Last Activity: {user['last_activity'] or 'Unknown'}\n\n"
        else:
            report += f"🎉 No inactive users found! All members have been active in the last {self.inactive_days} days.\n"
        
        return report


def main():
    parser = argparse.ArgumentParser(description="Find inactive users in a GitHub organization")
    parser.add_argument("--org", required=True, help="GitHub organization name")
    parser.add_argument("--token", help="GitHub personal access token (or set GITHUB_TOKEN env var)")
    parser.add_argument("--days", type=int, default=90, help="Number of days to consider a user inactive (default: 90)")
    parser.add_argument("--output", help="Output file path (optional)")

    args = parser.parse_args()
    
    # Get GitHub token
    token = args.token or os.getenv('GITHUB_TOKEN')
    if not token:
        print("Error: GitHub token is required. Set GITHUB_TOKEN environment variable or use --token")
        sys.exit(1)
    
    try:
        # Initialize the finder
        finder = GitHubInactiveUsersFinder(token, args.org, args.days)

        # Find inactive users
        inactive_users = finder.find_inactive_users()
        
        # Generate report
        report = finder.generate_report(inactive_users)
        
        # Output report
        if args.output:
            with open(args.output, 'w') as f:
                f.write(report)
            print(f"\nReport saved to: {args.output}")
        else:
            print("\n" + "=" * 60)
            print(report)
            
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
